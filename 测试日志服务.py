#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务测试脚本
用于测试日志查看服务.php的功能
"""

import requests
import json
import sys

def 测试日志服务():
    """测试日志服务的各项功能"""
    
    # 服务器地址
    服务器地址 = "http://localhost/日志查看服务.php"
    管理员密码 = "admin123456"
    
    print("=" * 60)
    print("日志服务测试开始")
    print("=" * 60)
    
    # 测试1: 获取日志文件列表
    print("\n1. 测试获取日志文件列表...")
    try:
        响应 = requests.post(服务器地址, 
                          json={
                              'action': 'get_log_files',
                              'admin_password': 管理员密码
                          },
                          timeout=10)
        
        if 响应.status_code == 200:
            结果 = 响应.json()
            print(f"   状态: {结果.get('status')}")
            print(f"   消息: {结果.get('message')}")
            if 结果.get('status') == 'success':
                文件列表 = 结果.get('data', [])
                print(f"   找到 {len(文件列表)} 个日志文件:")
                for 文件 in 文件列表:
                    print(f"     - {文件.get('name')} ({文件.get('size')} 字节)")
            else:
                print(f"   错误: {结果.get('message')}")
        else:
            print(f"   HTTP错误: {响应.status_code}")
            
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试2: 获取日志统计
    print("\n2. 测试获取日志统计...")
    try:
        响应 = requests.post(服务器地址, 
                          json={
                              'action': 'get_log_stats',
                              'file_path': 'system_logs/应用日志.txt',
                              'admin_password': 管理员密码
                          },
                          timeout=10)
        
        if 响应.status_code == 200:
            结果 = 响应.json()
            print(f"   状态: {结果.get('status')}")
            if 结果.get('status') == 'success':
                统计 = 结果.get('data', {})
                print(f"   总行数: {统计.get('total_lines', 0)}")
                print(f"   今日日志: {统计.get('today_count', 0)}")
                print(f"   24小时内: {统计.get('last_24h_count', 0)}")
                级别统计 = 统计.get('levels', {})
                print(f"   级别统计: {级别统计}")
            else:
                print(f"   错误: {结果.get('message')}")
        else:
            print(f"   HTTP错误: {响应.status_code}")
            
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试3: 读取日志内容
    print("\n3. 测试读取日志内容...")
    try:
        响应 = requests.post(服务器地址, 
                          json={
                              'action': 'read_log',
                              'file_path': 'system_logs/应用日志.txt',
                              'page': 1,
                              'per_page': 10,
                              'search': '',
                              'level': '',
                              'admin_password': 管理员密码
                          },
                          timeout=10)
        
        if 响应.status_code == 200:
            结果 = 响应.json()
            print(f"   状态: {结果.get('status')}")
            if 结果.get('status') == 'success':
                数据 = 结果.get('data', {})
                日志列表 = 数据.get('logs', [])
                分页信息 = 数据.get('pagination', {})
                
                print(f"   当前页: {分页信息.get('current_page')}")
                print(f"   总页数: {分页信息.get('total_pages')}")
                print(f"   总行数: {分页信息.get('total_lines')}")
                print(f"   本页日志条数: {len(日志列表)}")
                
                if 日志列表:
                    print("   最新几条日志:")
                    for i, 日志 in enumerate(日志列表[:3]):
                        print(f"     {i+1}. [{日志.get('level')}] {日志.get('timestamp')} - {日志.get('content')[:100]}...")
            else:
                print(f"   错误: {结果.get('message')}")
        else:
            print(f"   HTTP错误: {响应.status_code}")
            
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试4: 搜索日志
    print("\n4. 测试搜索日志...")
    try:
        响应 = requests.post(服务器地址, 
                          json={
                              'action': 'read_log',
                              'file_path': 'system_logs/应用日志.txt',
                              'page': 1,
                              'per_page': 5,
                              'search': 'ERROR',
                              'level': '',
                              'admin_password': 管理员密码
                          },
                          timeout=10)
        
        if 响应.status_code == 200:
            结果 = 响应.json()
            print(f"   状态: {结果.get('status')}")
            if 结果.get('status') == 'success':
                数据 = 结果.get('data', {})
                日志列表 = 数据.get('logs', [])
                print(f"   搜索到 {len(日志列表)} 条包含'ERROR'的日志")
                
                for i, 日志 in enumerate(日志列表):
                    print(f"     {i+1}. {日志.get('content')[:80]}...")
            else:
                print(f"   错误: {结果.get('message')}")
        else:
            print(f"   HTTP错误: {响应.status_code}")
            
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("日志服务测试完成")
    print("=" * 60)

if __name__ == "__main__":
    测试日志服务()
