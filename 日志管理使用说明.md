# 日志管理系统使用说明

## 概述

本系统为网络验证系统新增了完整的日志管理功能，包括：
- 日志文件读取和显示
- 日志搜索和过滤
- 日志统计分析
- 分页浏览
- 日志导出

## 文件说明

### 1. 日志查看服务.php
- **功能**: 后端日志处理服务
- **位置**: 项目根目录
- **作用**: 
  - 读取 `system_logs` 目录下的日志文件
  - 提供日志搜索、过滤、分页功能
  - 生成日志统计信息
  - 支持管理员权限验证

### 2. 超级管理员端.py (已修改)
- **功能**: 前端管理界面
- **修改内容**:
  - 新增"日志管理"导航菜单
  - 添加日志查看页面
  - 集成日志管理相关API
  - 优化日志显示样式

## 功能特性

### 1. 日志文件管理
- 自动扫描 `system_logs` 目录
- 显示文件大小和修改时间
- 支持切换不同日志文件

### 2. 日志内容显示
- 语法高亮显示
- 按行号显示
- 支持不同日志级别的颜色区分
- 响应式设计，适配移动端

### 3. 搜索和过滤
- **关键词搜索**: 支持中文搜索
- **级别过滤**: 按日志级别筛选
- **实时搜索**: 输入延迟500ms后自动搜索
- **组合过滤**: 支持同时使用多种过滤条件

### 4. 分页浏览
- 每页默认显示100条日志
- 支持上一页/下一页导航
- 显示当前页码和总页数
- 显示日志总数统计

### 5. 统计信息
- 总日志条数
- 今日日志数量
- 错误日志统计
- 24小时内日志数量
- 各级别日志分布

### 6. 导出功能
- 支持导出当前过滤条件下的日志
- 自动生成带时间戳的文件名
- 保持原始日志格式

## 使用方法

### 1. 访问日志管理
1. 启动超级管理员端: `python 客户端示例\超级管理员端.py`
2. 浏览器访问: `http://localhost:5000`
3. 点击左侧菜单"日志管理"

### 2. 查看日志
1. 选择要查看的日志文件
2. 系统自动加载日志内容和统计信息
3. 使用分页控件浏览不同页面的日志

### 3. 搜索日志
1. 在"搜索关键词"框输入要搜索的内容
2. 系统自动执行搜索并高亮显示结果
3. 支持中文和英文搜索

### 4. 过滤日志
1. 在"日志级别"下拉框选择要过滤的级别
2. 可选择: 调试信息、一般信息、警告信息、错误信息、严重错误
3. 与搜索功能可以组合使用

### 5. 导出日志
1. 设置好搜索和过滤条件
2. 点击"导出"按钮
3. 浏览器自动下载过滤后的日志文件

## 技术实现

### 后端 (PHP)
- 使用PHP读取日志文件
- JSON格式API响应
- 支持DES加密传输(可选)
- 管理员权限验证
- 错误处理和日志记录

### 前端 (Python Flask + JavaScript)
- Flask提供API代理服务
- jQuery处理前端交互
- Bootstrap美化界面
- 响应式设计
- 实时搜索和过滤

### 安全特性
- 管理员密码验证
- CSRF令牌保护
- 文件路径验证
- 输入参数过滤
- 错误信息脱敏

## 配置说明

### 日志目录配置
```php
// 在 日志查看服务.php 中
define('LOG_DIR', 'system_logs');                    // 日志存储目录
define('LOG_FILE', LOG_DIR . '/应用日志.txt');        // 主日志文件
define('MAX_LINES_PER_PAGE', 100);                   // 每页最大行数
```

### 管理员密码
```php
// 在 日志查看服务.php 中
define('ADMIN_PASSWORD', 'admin123456');             // 管理员密码
```

### 服务器地址配置
```python
# 在 超级管理员端.py 中
# 修改日志服务的调用地址
响应 = requests.post('http://localhost/日志查看服务.php', ...)
```

## 故障排除

### 1. 日志文件无法读取
- 检查 `system_logs` 目录是否存在
- 确认PHP有读取权限
- 检查日志文件是否存在

### 2. 搜索功能异常
- 确认搜索关键词编码正确
- 检查日志文件编码是否为UTF-8
- 验证PHP的mbstring扩展是否启用

### 3. 分页显示错误
- 检查日志文件是否过大
- 确认PHP内存限制足够
- 验证分页参数是否正确

### 4. 导出功能失败
- 检查浏览器是否支持下载
- 确认服务器响应头设置正确
- 验证文件权限设置

## 扩展功能

### 1. 日志轮转
系统支持自动日志轮转，当日志文件超过10MB时自动备份。

### 2. 实时监控
可以扩展为实时日志监控，使用WebSocket推送最新日志。

### 3. 日志分析
可以添加日志分析功能，如错误趋势图、访问统计等。

### 4. 多服务器支持
可以扩展为支持多台服务器的日志集中管理。

## 注意事项

1. **性能考虑**: 大日志文件可能影响加载速度，建议定期清理
2. **安全考虑**: 日志可能包含敏感信息，注意访问权限控制
3. **存储考虑**: 日志文件会持续增长，注意磁盘空间管理
4. **编码问题**: 确保所有日志文件使用UTF-8编码

## 更新日志

- **v1.0** (2024-07-31): 初始版本，基础日志管理功能
  - 日志文件读取和显示
  - 搜索和过滤功能
  - 分页浏览
  - 统计信息
  - 导出功能
