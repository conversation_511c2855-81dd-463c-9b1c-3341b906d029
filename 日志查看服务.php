<?php
/**
 * 日志查看服务端 - 读取和管理系统日志文件
 * 功能：读取日志文件、分页显示、搜索过滤、日志统计
 * 特性：支持多种日志格式、实时日志监控、安全访问控制
 * 作者：网络验证系统
 * 版本：1.0
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 系统配置参数
define('LOG_DIR', 'system_logs');                    // 日志存储目录
define('LOG_FILE', LOG_DIR . '/应用日志.txt');        // 主日志文件
define('ADMIN_PASSWORD', 'admin123456');             // 管理员密码
define('MAX_LINES_PER_PAGE', 100);                   // 每页最大行数
define('LOG_ENCODING', 'UTF-8');                     // 日志文件编码

// 检查运行环境
$is_cli = php_sapi_name() === 'cli';

// 设置响应头（仅在Web环境下）
if (!$is_cli) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

    // 处理CORS预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => 'CORS预检请求处理成功',
            'service' => '日志查看服务',
            'version' => '1.0'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * 验证管理员权限
 */
function 验证管理员权限($请求数据) {
    if (!isset($请求数据['admin_password']) || $请求数据['admin_password'] !== ADMIN_PASSWORD) {
        throw new Exception('管理员密码错误，无权访问日志');
    }
    return true;
}

/**
 * 获取日志文件列表
 */
function 获取日志文件列表() {
    $日志文件列表 = [];
    
    if (!is_dir(LOG_DIR)) {
        return $日志文件列表;
    }
    
    $文件列表 = scandir(LOG_DIR);
    foreach ($文件列表 as $文件名) {
        if ($文件名 === '.' || $文件名 === '..') {
            continue;
        }
        
        $文件路径 = LOG_DIR . '/' . $文件名;
        if (is_file($文件路径) && (strpos($文件名, '.txt') !== false || strpos($文件名, '.log') !== false)) {
            $文件信息 = [
                'name' => $文件名,
                'path' => $文件路径,
                'size' => filesize($文件路径),
                'modified' => date('Y-m-d H:i:s', filemtime($文件路径)),
                'readable' => is_readable($文件路径)
            ];
            $日志文件列表[] = $文件信息;
        }
    }
    
    // 按修改时间倒序排列
    usort($日志文件列表, function($a, $b) {
        return strtotime($b['modified']) - strtotime($a['modified']);
    });
    
    return $日志文件列表;
}

/**
 * 读取日志文件内容
 */
function 读取日志内容($文件路径, $页码 = 1, $每页行数 = MAX_LINES_PER_PAGE, $搜索关键词 = '', $日志级别 = '') {
    if (!file_exists($文件路径) || !is_readable($文件路径)) {
        throw new Exception('日志文件不存在或无法读取: ' . $文件路径);
    }
    
    // 读取文件所有行
    $所有行 = file($文件路径, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($所有行 === false) {
        throw new Exception('无法读取日志文件内容');
    }
    
    // 反转数组，最新的日志在前面
    $所有行 = array_reverse($所有行);
    
    // 过滤日志
    $过滤后的行 = [];
    foreach ($所有行 as $行号 => $行内容) {
        $符合条件 = true;
        
        // 搜索关键词过滤
        if (!empty($搜索关键词)) {
            if (stripos($行内容, $搜索关键词) === false) {
                $符合条件 = false;
            }
        }
        
        // 日志级别过滤
        if (!empty($日志级别) && $符合条件) {
            if (stripos($行内容, '[' . $日志级别) === false) {
                $符合条件 = false;
            }
        }
        
        if ($符合条件) {
            $过滤后的行[] = [
                'line_number' => count($所有行) - $行号,
                'content' => $行内容,
                'timestamp' => 提取时间戳($行内容),
                'level' => 提取日志级别($行内容)
            ];
        }
    }
    
    // 计算分页
    $总行数 = count($过滤后的行);
    $总页数 = ceil($总行数 / $每页行数);
    $起始位置 = ($页码 - 1) * $每页行数;
    $当前页数据 = array_slice($过滤后的行, $起始位置, $每页行数);
    
    return [
        'logs' => $当前页数据,
        'pagination' => [
            'current_page' => $页码,
            'total_pages' => $总页数,
            'total_lines' => $总行数,
            'per_page' => $每页行数
        ],
        'file_info' => [
            'path' => $文件路径,
            'size' => filesize($文件路径),
            'modified' => date('Y-m-d H:i:s', filemtime($文件路径))
        ]
    ];
}

/**
 * 提取时间戳
 */
function 提取时间戳($日志行) {
    if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $日志行, $匹配)) {
        return $匹配[1];
    }
    return '';
}

/**
 * 提取日志级别
 */
function 提取日志级别($日志行) {
    $级别列表 = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL', '调试信息', '一般信息', '警告信息', '错误信息', '严重错误', '系统错误'];
    
    foreach ($级别列表 as $级别) {
        if (stripos($日志行, '[' . $级别 . ']') !== false) {
            return $级别;
        }
    }
    return 'UNKNOWN';
}

/**
 * 获取日志统计信息
 */
function 获取日志统计($文件路径) {
    if (!file_exists($文件路径) || !is_readable($文件路径)) {
        return [];
    }
    
    $所有行 = file($文件路径, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($所有行 === false) {
        return [];
    }
    
    $统计信息 = [
        'total_lines' => count($所有行),
        'levels' => [
            'DEBUG' => 0,
            'INFO' => 0,
            'WARNING' => 0,
            'ERROR' => 0,
            'CRITICAL' => 0,
            'UNKNOWN' => 0
        ],
        'today_count' => 0,
        'last_24h_count' => 0
    ];
    
    $今天 = date('Y-m-d');
    $昨天时间 = time() - 24 * 3600;
    
    foreach ($所有行 as $行内容) {
        // 统计日志级别
        $级别 = 提取日志级别($行内容);
        if (isset($统计信息['levels'][$级别])) {
            $统计信息['levels'][$级别]++;
        } else {
            $统计信息['levels']['UNKNOWN']++;
        }
        
        // 统计今天的日志
        if (strpos($行内容, $今天) !== false) {
            $统计信息['today_count']++;
        }
        
        // 统计最近24小时的日志
        $时间戳 = 提取时间戳($行内容);
        if (!empty($时间戳) && strtotime($时间戳) > $昨天时间) {
            $统计信息['last_24h_count']++;
        }
    }
    
    return $统计信息;
}

/**
 * 处理请求
 */
function 处理请求() {
    global $is_cli;

    try {
        // 获取请求数据
        if ($is_cli) {
            // CLI模式下从stdin读取
            $输入数据 = stream_get_contents(STDIN);
        } else {
            // Web模式下从php://input读取
            $输入数据 = file_get_contents('php://input');
        }

        if (empty($输入数据)) {
            throw new Exception('请求数据为空');
        }
        
        $请求数据 = json_decode($输入数据, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON格式错误: ' . json_last_error_msg());
        }
        
        // 验证管理员权限
        验证管理员权限($请求数据);
        
        // 获取操作类型
        $操作 = $请求数据['action'] ?? '';
        
        switch ($操作) {
            case 'get_log_files':
                // 获取日志文件列表
                $文件列表 = 获取日志文件列表();
                return [
                    'status' => 'success',
                    'data' => $文件列表,
                    'message' => '获取日志文件列表成功'
                ];
                
            case 'read_log':
                // 读取日志内容
                $文件路径 = $请求数据['file_path'] ?? LOG_FILE;
                $页码 = intval($请求数据['page'] ?? 1);
                $每页行数 = intval($请求数据['per_page'] ?? MAX_LINES_PER_PAGE);
                $搜索关键词 = $请求数据['search'] ?? '';
                $日志级别 = $请求数据['level'] ?? '';
                
                $日志数据 = 读取日志内容($文件路径, $页码, $每页行数, $搜索关键词, $日志级别);
                return [
                    'status' => 'success',
                    'data' => $日志数据,
                    'message' => '读取日志内容成功'
                ];
                
            case 'get_log_stats':
                // 获取日志统计
                $文件路径 = $请求数据['file_path'] ?? LOG_FILE;
                $统计信息 = 获取日志统计($文件路径);
                return [
                    'status' => 'success',
                    'data' => $统计信息,
                    'message' => '获取日志统计成功'
                ];
                
            default:
                throw new Exception('不支持的操作类型: ' . $操作);
        }
        
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => $e->getMessage(),
            'error_code' => 'LOG_SERVICE_ERROR',
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// 处理请求并返回结果
$结果 = 处理请求();
echo json_encode($结果, JSON_UNESCAPED_UNICODE);
?>
